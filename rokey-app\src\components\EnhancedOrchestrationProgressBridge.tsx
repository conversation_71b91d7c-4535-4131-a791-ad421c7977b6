'use client';

import React, { useEffect, useCallback } from 'react';
import EnhancedOrchestrationProgressTracker from './EnhancedOrchestrationProgressTracker';
import { useEnhancedOrchestrationTracking, ENHANCED_ORCHESTRATION_STEPS } from '@/hooks/useEnhancedOrchestrationTracking';
import { type ProgressCallback } from '@/lib/langgraph-orchestration/RouKeyLangGraphIntegration';

interface EnhancedOrchestrationProgressBridgeProps {
  isActive: boolean;
  onProgressCallback?: (callback: ProgressCallback) => void;
  className?: string;
  title?: string;
  showHeader?: boolean;
}

export default function EnhancedOrchestrationProgressBridge({
  isActive,
  onProgressCallback,
  className = '',
  title = "RouKey AI Orchestration",
  showHeader = true
}: EnhancedOrchestrationProgressBridgeProps) {
  console.log('🎯 EnhancedOrchestrationProgressBridge render:', { isActive, className });

  const {
    steps,
    isActive: trackingActive,
    trackEvent,
    completeStep,
    updateStepProgress,
    startTracking,
    completeTracking,
    resetTracking
  } = useEnhancedOrchestrationTracking();

  console.log('🎯 Enhanced Progress state:', { steps: steps.length, trackingActive, isActive });

  // Create enhanced progress callback for orchestration system
  const progressCallback: ProgressCallback = useCallback({
    onClassificationStart: () => {
      trackEvent(ENHANCED_ORCHESTRATION_STEPS.MULTI_ROLE.CLASSIFICATION);
      // Simulate progress updates
      setTimeout(() => updateStepProgress('classification', 30, 'Analyzing request complexity...'), 500);
      setTimeout(() => updateStepProgress('classification', 60, 'Identifying required expertise...'), 1000);
      setTimeout(() => updateStepProgress('classification', 90, 'Finalizing analysis...'), 1500);
    },
    
    onClassificationComplete: (roles: string[], threshold: number) => {
      completeStep('classification', [
        `✨ Identified ${roles.length} specialist areas needed`,
        `🎯 Analysis completed with ${Math.round(threshold * 100)}% confidence`,
        `🚀 Ready to assemble your AI dream team`
      ]);
    },
    
    onRoleSelectionComplete: (selectedRoles: string[], filteredRoles: string[]) => {
      trackEvent({
        ...ENHANCED_ORCHESTRATION_STEPS.MULTI_ROLE.ROLE_SELECTION,
        details: [
          `🎯 Selected ${selectedRoles.length} AI specialists`,
          `⚡ Each expert optimized for your specific needs`,
          `🤝 Team assembly complete and ready to collaborate`
        ]
      });
      setTimeout(() => completeStep('role_selection'), 100);
    },
    
    onWorkflowSelectionComplete: (workflowType: string, reasoning: string) => {
      const workflowNames = {
        'sequential': '📋 Step-by-step collaboration',
        'supervisor': '👑 Coordinated teamwork',
        'hierarchical': '🏗️ Multi-level coordination',
        'parallel': '⚡ Simultaneous processing'
      };
      
      trackEvent({
        ...ENHANCED_ORCHESTRATION_STEPS.MULTI_ROLE.WORKFLOW_SELECTION,
        description: `${workflowNames[workflowType as keyof typeof workflowNames] || '🤖 Smart collaboration'} approach selected`,
        details: [
          `🎯 Strategy: ${workflowNames[workflowType as keyof typeof workflowNames] || workflowType}`,
          `🧠 Optimized for your specific request type`,
          `⚡ Team coordination plan established`
        ]
      });
      setTimeout(() => completeStep('workflow_selection'), 100);
    },
    
    onAgentCreationStart: () => {
      trackEvent(ENHANCED_ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION);
      // Simulate detailed agent creation progress
      setTimeout(() => updateStepProgress('agent_creation', 25, 'Configuring AI specialist parameters...'), 300);
      setTimeout(() => updateStepProgress('agent_creation', 50, 'Loading specialized knowledge bases...'), 800);
      setTimeout(() => updateStepProgress('agent_creation', 75, 'Establishing secure API connections...'), 1300);
      setTimeout(() => updateStepProgress('agent_creation', 95, 'Finalizing team setup...'), 1800);
    },
    
    onAgentCreationComplete: (agents: Array<{ role: string, apiKey: string }>) => {
      completeStep('agent_creation', [
        `🤖 ${agents.length} AI specialists ready to work`,
        `⚙️ Each expert configured with optimal settings`,
        `🚀 Team assembly complete and ready to collaborate`
      ]);
    },
    
    onSupervisorInitStart: () => {
      trackEvent(ENHANCED_ORCHESTRATION_STEPS.MULTI_ROLE.SUPERVISOR_INIT);
      setTimeout(() => updateStepProgress('supervisor_init', 50, 'Establishing team communication channels...'), 400);
      setTimeout(() => updateStepProgress('supervisor_init', 90, 'Activating coordination protocols...'), 800);
    },
    
    onSupervisorInitComplete: (supervisorRole: string) => {
      completeStep('supervisor_init', [
        '✨ Team coordinator assigned and ready',
        '📡 Communication channels established',
        '🎯 Ready to manage collaborative workflow'
      ]);
    },
    
    onTaskPlanningStart: () => {
      trackEvent(ENHANCED_ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);
      setTimeout(() => updateStepProgress('task_planning', 40, 'Breaking down complex requirements...'), 500);
      setTimeout(() => updateStepProgress('task_planning', 80, 'Assigning tasks to specialists...'), 1000);
    },
    
    onTaskPlanningComplete: (plan: string) => {
      completeStep('task_planning', [
        '📋 Task breakdown completed successfully',
        '🎯 Each specialist assigned optimal workload',
        '⚡ Ready to begin collaborative execution'
      ]);
    },
    
    onAgentWorkStart: (role: string, task: string) => {
      const roleNames = {
        'brainstorming_ideation': '💡 Creative Ideation Specialist',
        'writing': '✍️ Content Writing Expert',
        'coding_backend': '⚙️ Backend Development Expert',
        'coding_frontend': '🎨 Frontend Development Expert',
        'general_chat': '🤖 General AI Assistant'
      };
      
      const agentName = roleNames[role as keyof typeof roleNames] || '🤖 AI Specialist';
      
      trackEvent({
        type: 'agent_working',
        stepId: `agent_${role}`,
        title: `${agentName} Working`,
        description: task,
        color: getColorForRole(role),
        icon: getIconForRole(role),
        agent: agentName,
        duration: 15,
        details: [
          `🎯 Specialist: ${agentName}`,
          '🧠 Applying deep expertise to your request',
          '⚡ Processing and generating high-quality response...'
        ]
      });
      
      // Simulate work progress
      setTimeout(() => updateStepProgress(`agent_${role}`, 20, 'Analyzing requirements...'), 1000);
      setTimeout(() => updateStepProgress(`agent_${role}`, 40, 'Generating initial ideas...'), 3000);
      setTimeout(() => updateStepProgress(`agent_${role}`, 60, 'Refining and optimizing...'), 6000);
      setTimeout(() => updateStepProgress(`agent_${role}`, 80, 'Finalizing contribution...'), 9000);
    },
    
    onAgentWorkComplete: (role: string, result: string) => {
      const roleNames = {
        'brainstorming_ideation': '💡 Creative Ideation Specialist',
        'writing': '✍️ Content Writing Expert',
        'coding_backend': '⚙️ Backend Development Expert',
        'coding_frontend': '🎨 Frontend Development Expert',
        'general_chat': '🤖 General AI Assistant'
      };
      
      const agentName = roleNames[role as keyof typeof roleNames] || '🤖 Specialist';
      
      completeStep(`agent_${role}`, [
        `✅ ${agentName} work completed successfully`,
        `🎯 High-quality response generated (${result.length} chars)`,
        '🤝 Contribution ready for team integration'
      ]);
    },
    
    onSupervisorSynthesisStart: () => {
      trackEvent({
        type: 'supervisor_synthesis',
        stepId: 'supervisor_synthesis',
        title: '🧪 Team Coordinator: Combining Results',
        description: 'Creating comprehensive final response from all specialist contributions...',
        color: 'emerald',
        icon: 'synthesis',
        duration: 5,
        details: [
          '📊 Reviewing all specialist contributions',
          '🔄 Combining insights into unified response',
          '✨ Ensuring quality and coherence'
        ]
      });
      
      setTimeout(() => updateStepProgress('supervisor_synthesis', 30, 'Analyzing specialist contributions...'), 500);
      setTimeout(() => updateStepProgress('supervisor_synthesis', 60, 'Synthesizing unified response...'), 1500);
      setTimeout(() => updateStepProgress('supervisor_synthesis', 90, 'Final quality check...'), 2500);
    },
    
    onSupervisorSynthesisComplete: (synthesis: string) => {
      completeStep('supervisor_synthesis', [
        '✅ Final response synthesis completed',
        '🎯 All specialist insights successfully combined',
        '🚀 Collaborative work finished successfully'
      ]);
    },
    
    onOrchestrationComplete: (result: any) => {
      completeTracking();
    },
    
    onError: (step: string, error: string) => {
      trackEvent({
        type: 'error',
        stepId: `error_${step}`,
        title: `❌ Error in ${step}`,
        description: error,
        color: 'red',
        icon: 'validating',
        details: [
          `🔍 Step: ${step}`,
          `⚠️ Error: ${error}`,
          '🔄 Attempting recovery...'
        ]
      });
    }
  }, [trackEvent, completeStep, updateStepProgress, completeTracking]);

  // Provide progress callback to parent
  useEffect(() => {
    if (onProgressCallback) {
      onProgressCallback(progressCallback);
    }
  }, [onProgressCallback, progressCallback]);

  // Enhanced start/stop tracking with more comprehensive simulation
  useEffect(() => {
    if (isActive && !trackingActive) {
      startTracking();

      // Enhanced simulation with more steps and realistic timing
      const simulateEnhancedFlow = async () => {
        // Step 1: Initial connection
        setTimeout(() => {
          trackEvent({
            ...ENHANCED_ORCHESTRATION_STEPS.DETAILED.CONNECTING,
            progress: 0
          });
        }, 300);

        setTimeout(() => {
          updateStepProgress('connecting', 100);
          completeStep('connecting', ['🔗 Secure connections established', '⚡ Ready to begin analysis']);
        }, 1000);

        // Step 2: Classification
        setTimeout(() => {
          trackEvent(ENHANCED_ORCHESTRATION_STEPS.MULTI_ROLE.CLASSIFICATION);
        }, 1200);

        setTimeout(() => {
          completeStep('classification', [
            '🎯 Request analysis completed',
            '🧠 Optimal approach identified',
            '🚀 Ready to process your request'
          ]);
        }, 2500);

        // Step 3: Agent preparation
        setTimeout(() => {
          trackEvent({
            ...ENHANCED_ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION,
            title: '🤖 Preparing AI Specialist',
            description: 'Setting up the perfect expert for your task...'
          });
        }, 2800);

        setTimeout(() => {
          completeStep('agent_creation', [
            '🤖 AI specialist ready to work',
            '⚙️ Expert configured with optimal settings',
            '⚡ Processing your request...'
          ]);
        }, 4500);

        // Step 4: Optimization
        setTimeout(() => {
          trackEvent(ENHANCED_ORCHESTRATION_STEPS.DETAILED.OPTIMIZING);
        }, 4800);

        setTimeout(() => {
          completeStep('optimizing', [
            '⚡ Performance optimized',
            '🎯 System tuned for best results',
            '✨ Ready for final processing'
          ]);
        }, 6000);
      };

      simulateEnhancedFlow();

    } else if (!isActive && trackingActive) {
      // Small delay before resetting to show final state
      setTimeout(() => {
        resetTracking();
      }, 4000);
    }
  }, [isActive, trackingActive, startTracking, resetTracking, trackEvent, completeStep, updateStepProgress]);

  console.log('🎯 Enhanced render decision:', { trackingActive, stepsLength: steps.length, shouldRender: trackingActive && steps.length > 0 });

  if (!trackingActive || steps.length === 0) {
    console.log('🎯 Not rendering enhanced - trackingActive:', trackingActive, 'steps:', steps.length);
    return null;
  }

  console.log('🎯 Rendering enhanced progress tracker with steps:', steps);

  return (
    <div className={className}>
      <EnhancedOrchestrationProgressTracker
        steps={steps}
        isActive={trackingActive}
        autoScroll={true}
        showHeader={showHeader}
        title={title}
      />
    </div>
  );
}

// Helper functions for role-specific styling
function getColorForRole(role: string): 'blue' | 'purple' | 'green' | 'orange' | 'pink' | 'indigo' | 'teal' | 'red' | 'yellow' | 'emerald' | 'cyan' | 'rose' {
  const colorMap: Record<string, any> = {
    'brainstorming_ideation': 'purple',
    'writing': 'blue',
    'coding_backend': 'teal',
    'coding_frontend': 'pink',
    'general_chat': 'orange'
  };
  return colorMap[role] || 'cyan';
}

function getIconForRole(role: string): 'analysis' | 'roles' | 'workflow' | 'agents' | 'supervisor' | 'planning' | 'working' | 'synthesis' | 'connecting' | 'generating' | 'classification' | 'thinking' | 'building' | 'chatting' | 'writing' | 'coding' | 'designing' | 'learning' | 'optimizing' | 'validating' {
  const iconMap: Record<string, any> = {
    'brainstorming_ideation': 'thinking',
    'writing': 'writing',
    'coding_backend': 'coding',
    'coding_frontend': 'designing',
    'general_chat': 'chatting'
  };
  return iconMap[role] || 'working';
}
