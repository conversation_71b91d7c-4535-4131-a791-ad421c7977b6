'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import OrchestrationProgressCard, { ProgressStep } from './OrchestrationProgressCard';

interface OrchestrationProgressTrackerProps {
  steps: ProgressStep[];
  isActive?: boolean;
  onStepUpdate?: (stepId: string, updates: Partial<ProgressStep>) => void;
  className?: string;
  autoScroll?: boolean;
}

export default function OrchestrationProgressTracker({
  steps,
  isActive = false,
  onStepUpdate,
  className = '',
  autoScroll = true
}: OrchestrationProgressTrackerProps) {
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
  const containerRef = useRef<HTMLDivElement>(null);
  const lastStepRef = useRef<HTMLDivElement>(null);
  const prevStepsLengthRef = useRef(steps.length);

  // Auto-scroll to new cards when they appear
  useEffect(() => {
    if (autoScroll && steps.length > prevStepsLengthRef.current && lastStepRef.current) {
      // Small delay to ensure the card is rendered
      setTimeout(() => {
        lastStepRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'nearest',
          inline: 'nearest'
        });
      }, 100);
    }
    prevStepsLengthRef.current = steps.length;
  }, [steps.length, autoScroll]);

  // Auto-scroll when a step status changes to in_progress
  useEffect(() => {
    const inProgressStep = steps.find(step => step.status === 'in_progress');
    if (autoScroll && inProgressStep && containerRef.current) {
      const stepElement = containerRef.current.querySelector(`[data-step-id="${inProgressStep.id}"]`);
      if (stepElement) {
        setTimeout(() => {
          stepElement.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'nearest',
            inline: 'nearest'
          });
        }, 100);
      }
    }
  }, [steps, autoScroll]);

  const handleToggleExpand = useCallback((stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepId)) {
        newSet.delete(stepId);
      } else {
        newSet.add(stepId);
      }
      return newSet;
    });
  }, []);

  if (!isActive || steps.length === 0) {
    return null;
  }

  return (
    <div 
      ref={containerRef}
      className={`space-y-3 ${className}`}
    >
      {steps.map((step, index) => (
        <div
          key={step.id}
          data-step-id={step.id}
          ref={index === steps.length - 1 ? lastStepRef : undefined}
          className="animate-in slide-in-from-top-2 duration-300"
          style={{ animationDelay: `${index * 50}ms` }}
        >
          <OrchestrationProgressCard
            step={step}
            isExpanded={expandedSteps.has(step.id)}
            onToggleExpand={() => handleToggleExpand(step.id)}
          />
        </div>
      ))}
    </div>
  );
}

// Hook for managing orchestration progress
export function useOrchestrationProgress() {
  const [steps, setSteps] = useState<ProgressStep[]>([]);
  const [isActive, setIsActive] = useState(false);

  const addStep = useCallback((step: Omit<ProgressStep, 'timestamp'>) => {
    const newStep: ProgressStep = {
      ...step,
      timestamp: new Date()
    };
    setSteps(prev => [...prev, newStep]);
    setIsActive(true);
  }, []);

  const updateStep = useCallback((stepId: string, updates: Partial<ProgressStep>) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, ...updates, timestamp: updates.status ? new Date() : step.timestamp }
        : step
    ));
  }, []);

  const completeStep = useCallback((stepId: string, details?: string[]) => {
    updateStep(stepId, { 
      status: 'completed',
      ...(details && { details })
    });
  }, [updateStep]);

  const startStep = useCallback((stepId: string, description?: string) => {
    updateStep(stepId, { 
      status: 'in_progress',
      ...(description && { description })
    });
  }, [updateStep]);

  const errorStep = useCallback((stepId: string, error: string) => {
    updateStep(stepId, { 
      status: 'error',
      description: error
    });
  }, [updateStep]);

  const reset = useCallback(() => {
    setSteps([]);
    setIsActive(false);
  }, []);

  const complete = useCallback(() => {
    setIsActive(false);
  }, []);

  return {
    steps,
    isActive,
    addStep,
    updateStep,
    completeStep,
    startStep,
    errorStep,
    reset,
    complete
  };
}
