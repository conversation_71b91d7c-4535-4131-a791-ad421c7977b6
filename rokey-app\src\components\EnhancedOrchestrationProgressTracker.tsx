'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import EnhancedOrchestrationProgressCard, { EnhancedProgressStep } from './EnhancedOrchestrationProgressCard';
import { SparklesIcon, RocketLaunchIcon } from '@heroicons/react/24/outline';

interface EnhancedOrchestrationProgressTrackerProps {
  steps: EnhancedProgressStep[];
  isActive?: boolean;
  onStepUpdate?: (stepId: string, updates: Partial<EnhancedProgressStep>) => void;
  className?: string;
  autoScroll?: boolean;
  showHeader?: boolean;
  title?: string;
}

export default function EnhancedOrchestrationProgressTracker({
  steps,
  isActive = false,
  onStepUpdate,
  className = '',
  autoScroll = true,
  showHeader = true,
  title = "RouKey AI Orchestration"
}: EnhancedOrchestrationProgressTrackerProps) {
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
  const [visibleSteps, setVisibleSteps] = useState<EnhancedProgressStep[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const lastStepRef = useRef<HTMLDivElement>(null);
  const prevStepsLengthRef = useRef(steps.length);

  // Staggered step appearance animation
  useEffect(() => {
    if (steps.length > prevStepsLengthRef.current) {
      const newSteps = steps.slice(prevStepsLengthRef.current);
      
      newSteps.forEach((step, index) => {
        setTimeout(() => {
          setVisibleSteps(prev => [...prev, step]);
        }, index * 200); // Stagger by 200ms
      });
    } else {
      setVisibleSteps(steps);
    }
    
    prevStepsLengthRef.current = steps.length;
  }, [steps]);

  // Auto-scroll to latest step
  useEffect(() => {
    if (autoScroll && lastStepRef.current && visibleSteps.length > 0) {
      const timer = setTimeout(() => {
        lastStepRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'nearest',
          inline: 'nearest'
        });
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [visibleSteps.length, autoScroll]);

  const handleToggleExpand = useCallback((stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepId)) {
        newSet.delete(stepId);
      } else {
        newSet.add(stepId);
      }
      return newSet;
    });
  }, []);

  const getStepStats = () => {
    const completed = visibleSteps.filter(step => step.status === 'completed').length;
    const inProgress = visibleSteps.filter(step => step.status === 'in_progress').length;
    const pending = visibleSteps.filter(step => step.status === 'pending').length;
    const errors = visibleSteps.filter(step => step.status === 'error').length;
    
    return { completed, inProgress, pending, errors, total: visibleSteps.length };
  };

  const stats = getStepStats();
  const progressPercentage = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;

  if (!isActive && visibleSteps.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with progress overview */}
      {showHeader && (
        <div className="bg-gradient-to-r from-slate-900/95 to-slate-800/95 backdrop-blur-sm rounded-2xl border border-slate-700/50 p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <SparklesIcon className="w-8 h-8 text-orange-400" />
                {isActive && (
                  <div className="absolute inset-0 rounded-full bg-orange-400/20 animate-ping" />
                )}
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">{title}</h2>
                <p className="text-sm text-slate-300">
                  {stats.inProgress > 0 ? 'Processing your request...' : 
                   stats.completed === stats.total && stats.total > 0 ? 'Orchestration complete!' :
                   'Preparing AI specialists...'}
                </p>
              </div>
            </div>
            
            {/* Progress indicator */}
            <div className="text-right">
              <div className="text-2xl font-bold text-white mb-1">
                {progressPercentage}%
              </div>
              <div className="text-xs text-slate-400">
                {stats.completed}/{stats.total} steps
              </div>
            </div>
          </div>

          {/* Progress bar */}
          <div className="relative">
            <div className="h-3 bg-slate-800 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-orange-500 to-orange-400 transition-all duration-1000 ease-out relative overflow-hidden"
                style={{ width: `${progressPercentage}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
              </div>
            </div>
            
            {/* Status indicators */}
            <div className="flex justify-between items-center mt-3 text-xs">
              <div className="flex space-x-4">
                {stats.inProgress > 0 && (
                  <div className="flex items-center space-x-1 text-orange-400">
                    <div className="w-2 h-2 rounded-full bg-orange-400 animate-pulse" />
                    <span>{stats.inProgress} active</span>
                  </div>
                )}
                {stats.completed > 0 && (
                  <div className="flex items-center space-x-1 text-green-400">
                    <div className="w-2 h-2 rounded-full bg-green-400" />
                    <span>{stats.completed} completed</span>
                  </div>
                )}
                {stats.errors > 0 && (
                  <div className="flex items-center space-x-1 text-red-400">
                    <div className="w-2 h-2 rounded-full bg-red-400 animate-pulse" />
                    <span>{stats.errors} errors</span>
                  </div>
                )}
              </div>
              
              {stats.completed === stats.total && stats.total > 0 && (
                <div className="flex items-center space-x-1 text-green-400">
                  <RocketLaunchIcon className="w-4 h-4" />
                  <span>All done!</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Progress steps */}
      <div 
        ref={containerRef}
        className="space-y-4"
      >
        {visibleSteps.map((step, index) => (
          <div
            key={step.id}
            data-step-id={step.id}
            ref={index === visibleSteps.length - 1 ? lastStepRef : undefined}
            className="animate-in slide-in-from-top-4 duration-500"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <EnhancedOrchestrationProgressCard
              step={step}
              isExpanded={expandedSteps.has(step.id)}
              onToggleExpand={() => handleToggleExpand(step.id)}
              animationDelay={index * 50}
              showProgress={true}
            />
          </div>
        ))}
      </div>

      {/* Completion celebration */}
      {stats.completed === stats.total && stats.total > 0 && isActive && (
        <div className="text-center py-8">
          <div className="inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 rounded-full text-green-300 shadow-lg shadow-green-500/20">
            <RocketLaunchIcon className="w-5 h-5" />
            <span className="font-semibold">Orchestration Complete!</span>
            <div className="w-2 h-2 rounded-full bg-green-400 animate-ping" />
          </div>
        </div>
      )}
    </div>
  );
}

// Hook for managing enhanced orchestration progress
export function useEnhancedOrchestrationProgress() {
  const [steps, setSteps] = useState<EnhancedProgressStep[]>([]);
  const [isActive, setIsActive] = useState(false);

  const addStep = useCallback((step: Omit<EnhancedProgressStep, 'timestamp'>) => {
    const newStep: EnhancedProgressStep = {
      ...step,
      timestamp: new Date()
    };
    setSteps(prev => [...prev, newStep]);
    setIsActive(true);
  }, []);

  const updateStep = useCallback((stepId: string, updates: Partial<EnhancedProgressStep>) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, ...updates, timestamp: updates.status ? new Date() : step.timestamp }
        : step
    ));
  }, []);

  const completeStep = useCallback((stepId: string, details?: string[]) => {
    updateStep(stepId, { 
      status: 'completed',
      progress: 100,
      ...(details && { details })
    });
  }, [updateStep]);

  const startStep = useCallback((stepId: string, description?: string, progress?: number) => {
    updateStep(stepId, { 
      status: 'in_progress',
      progress: progress || 0,
      ...(description && { description })
    });
  }, [updateStep]);

  const errorStep = useCallback((stepId: string, error: string) => {
    updateStep(stepId, { 
      status: 'error',
      description: error
    });
  }, [updateStep]);

  const reset = useCallback(() => {
    setSteps([]);
    setIsActive(false);
  }, []);

  const complete = useCallback(() => {
    // Mark any in-progress steps as completed
    setSteps(prev => prev.map(step => 
      step.status === 'in_progress' 
        ? { ...step, status: 'completed' as const, progress: 100, timestamp: new Date() }
        : step
    ));
    
    // Keep active for celebration animation
    setTimeout(() => {
      setIsActive(false);
    }, 3000);
  }, []);

  return {
    steps,
    isActive,
    addStep,
    updateStep,
    completeStep,
    startStep,
    errorStep,
    reset,
    complete
  };
}
