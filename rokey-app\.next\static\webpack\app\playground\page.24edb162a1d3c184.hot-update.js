"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts":
/*!******************************************!*\
  !*** ./src/hooks/useOrchestrationSSE.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOrchestrationSSE: () => (/* binding */ useOrchestrationSSE)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useOrchestrationSSE auto */ \nfunction useOrchestrationSSE(sessionId) {\n    const [steps, setSteps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttempts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const maxReconnectAttempts = 5;\n    const convertToProgressStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[convertToProgressStep]\": (step)=>{\n            return {\n                id: step.id,\n                title: step.title,\n                description: step.description,\n                status: step.status,\n                timestamp: new Date(step.timestamp),\n                details: step.details,\n                metadata: step.metadata,\n                icon: getIconForStep(step.id)\n            };\n        }\n    }[\"useOrchestrationSSE.useCallback[convertToProgressStep]\"], []);\n    const getIconForStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[getIconForStep]\": (stepId)=>{\n            if (stepId.includes('analyzing') || stepId.includes('classification')) return 'analysis';\n            if (stepId.includes('specialist') || stepId.includes('role')) return 'roles';\n            if (stepId.includes('orchestration') || stepId.includes('workflow')) return 'workflow';\n            if (stepId.includes('agent') || stepId.includes('creating')) return 'agents';\n            if (stepId.includes('supervisor')) return 'supervisor';\n            if (stepId.includes('planning') || stepId.includes('task')) return 'planning';\n            if (stepId.includes('working') || stepId.includes('agent_')) return 'working';\n            if (stepId.includes('synthesis') || stepId.includes('combining')) return 'synthesis';\n            if (stepId.includes('generating') || stepId.includes('response')) return 'generating';\n            return 'connecting';\n        }\n    }[\"useOrchestrationSSE.useCallback[getIconForStep]\"], []);\n    const connectToSSE = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[connectToSSE]\": ()=>{\n            if (!sessionId || eventSourceRef.current) {\n                console.log(\"[SSE] Connection skipped - sessionId: \".concat(sessionId, \", existing connection: \").concat(!!eventSourceRef.current));\n                return;\n            }\n            console.log(\"[SSE] Connecting to orchestration progress: \".concat(sessionId));\n            console.log(\"[SSE] Full URL: /api/orchestration/progress?sessionId=\".concat(sessionId));\n            try {\n                const eventSource = new EventSource(\"/api/orchestration/progress?sessionId=\".concat(sessionId));\n                eventSourceRef.current = eventSource;\n                console.log(\"[SSE] EventSource created successfully\");\n                eventSource.onopen = ({\n                    \"useOrchestrationSSE.useCallback[connectToSSE]\": ()=>{\n                        console.log('[SSE] Connected to orchestration progress stream');\n                        reconnectAttempts.current = 0;\n                        setIsActive(true);\n                        setError(null);\n                    }\n                })[\"useOrchestrationSSE.useCallback[connectToSSE]\"];\n                eventSource.onmessage = ({\n                    \"useOrchestrationSSE.useCallback[connectToSSE]\": (event)=>{\n                        try {\n                            const data = JSON.parse(event.data);\n                            console.log('[SSE] Received progress data:', data);\n                            switch(data.type){\n                                case 'initial':\n                                    if (data.steps) {\n                                        setSteps(data.steps.map(convertToProgressStep));\n                                    }\n                                    if (data.isComplete) {\n                                        setIsComplete(true);\n                                        setIsActive(false);\n                                    }\n                                    if (data.error) {\n                                        setError(data.error);\n                                    }\n                                    break;\n                                case 'step_update':\n                                    if (data.step) {\n                                        const newStep = convertToProgressStep(data.step);\n                                        setSteps({\n                                            \"useOrchestrationSSE.useCallback[connectToSSE]\": (prev)=>{\n                                                const existingIndex = prev.findIndex({\n                                                    \"useOrchestrationSSE.useCallback[connectToSSE].existingIndex\": (s)=>s.id === newStep.id\n                                                }[\"useOrchestrationSSE.useCallback[connectToSSE].existingIndex\"]);\n                                                if (existingIndex >= 0) {\n                                                    // Update existing step\n                                                    const updated = [\n                                                        ...prev\n                                                    ];\n                                                    updated[existingIndex] = newStep;\n                                                    return updated;\n                                                } else {\n                                                    // Add new step\n                                                    return [\n                                                        ...prev,\n                                                        newStep\n                                                    ];\n                                                }\n                                            }\n                                        }[\"useOrchestrationSSE.useCallback[connectToSSE]\"]);\n                                    }\n                                    break;\n                                case 'complete':\n                                    console.log('[SSE] Orchestration completed');\n                                    setIsComplete(true);\n                                    setIsActive(false);\n                                    // Keep connection open briefly to show final state\n                                    setTimeout({\n                                        \"useOrchestrationSSE.useCallback[connectToSSE]\": ()=>{\n                                            eventSource.close();\n                                        }\n                                    }[\"useOrchestrationSSE.useCallback[connectToSSE]\"], 3000);\n                                    break;\n                                case 'error':\n                                    console.error('[SSE] Orchestration error:', data.error);\n                                    setError(data.error || 'Unknown error occurred');\n                                    setIsActive(false);\n                                    break;\n                                case 'heartbeat':\n                                    break;\n                                default:\n                                    console.warn('[SSE] Unknown message type:', data.type);\n                            }\n                        } catch (parseError) {\n                            console.error('[SSE] Error parsing progress data:', parseError);\n                        }\n                    }\n                })[\"useOrchestrationSSE.useCallback[connectToSSE]\"];\n                eventSource.onerror = ({\n                    \"useOrchestrationSSE.useCallback[connectToSSE]\": (error)=>{\n                        console.error('[SSE] Connection error:', error);\n                        eventSource.close();\n                        eventSourceRef.current = null;\n                        // Attempt to reconnect with exponential backoff\n                        if (reconnectAttempts.current < maxReconnectAttempts) {\n                            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 10000);\n                            console.log(\"[SSE] Reconnecting in \".concat(delay, \"ms (attempt \").concat(reconnectAttempts.current + 1, \"/\").concat(maxReconnectAttempts, \")\"));\n                            reconnectTimeoutRef.current = setTimeout({\n                                \"useOrchestrationSSE.useCallback[connectToSSE]\": ()=>{\n                                    reconnectAttempts.current++;\n                                    connectToSSE();\n                                }\n                            }[\"useOrchestrationSSE.useCallback[connectToSSE]\"], delay);\n                        } else {\n                            console.error('[SSE] Max reconnection attempts reached');\n                            setError('Connection lost. Please refresh the page.');\n                            setIsActive(false);\n                        }\n                    }\n                })[\"useOrchestrationSSE.useCallback[connectToSSE]\"];\n            } catch (error) {\n                console.error('[SSE] Failed to create EventSource:', error);\n                setError('Failed to connect to progress stream');\n            }\n        }\n    }[\"useOrchestrationSSE.useCallback[connectToSSE]\"], [\n        sessionId,\n        convertToProgressStep\n    ]);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[disconnect]\": ()=>{\n            if (eventSourceRef.current) {\n                console.log('[SSE] Disconnecting from orchestration progress');\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n            setIsActive(false);\n        }\n    }[\"useOrchestrationSSE.useCallback[disconnect]\"], []);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[reset]\": ()=>{\n            disconnect();\n            setSteps([]);\n            setIsComplete(false);\n            setError(null);\n            reconnectAttempts.current = 0;\n        }\n    }[\"useOrchestrationSSE.useCallback[reset]\"], [\n        disconnect\n    ]);\n    // Connect when sessionId is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationSSE.useEffect\": ()=>{\n            if (sessionId) {\n                connectToSSE();\n            } else {\n                disconnect();\n            }\n            return ({\n                \"useOrchestrationSSE.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useOrchestrationSSE.useEffect\"];\n        }\n    }[\"useOrchestrationSSE.useEffect\"], [\n        sessionId,\n        connectToSSE,\n        disconnect\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationSSE.useEffect\": ()=>{\n            return ({\n                \"useOrchestrationSSE.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useOrchestrationSSE.useEffect\"];\n        }\n    }[\"useOrchestrationSSE.useEffect\"], [\n        disconnect\n    ]);\n    return {\n        steps,\n        isActive,\n        isComplete,\n        error,\n        connect: connectToSSE,\n        disconnect,\n        reset\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts\n"));

/***/ })

});